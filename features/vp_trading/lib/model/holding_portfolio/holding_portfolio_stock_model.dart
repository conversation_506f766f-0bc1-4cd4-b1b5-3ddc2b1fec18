import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:vp_common/utils/format_utils.dart';
import 'package:vp_common/utils/money_utils.dart';
import 'package:vp_core/vp_core.dart';

part 'holding_portfolio_stock_model.g.dart';

@JsonSerializable()
class HoldingPortfolioStockModel {
  final String? custodyCd;
  final String? accountId;
  final String? productTypeCd;
  final String? symbol;
  final String? secType;
  final String? market;
  final num? total;
  final num? trade;
  final num? blocked;
  final num? vsdMortgage;
  final num? mortgage;
  final num? restrict;
  final num? receivingRight;
  final num? receivingT0;
  final num? receivingT1;
  final num? receivingT2;
  final double? costPrice;
  final double? costPriceAmt;
  final num? basicPrice;
  final num? basicPriceAmt;
  final String? marginAmt;
  final String? pnlAmt;
  final String? pnlRate;
  final String? isSell;
  final num? withdraw;
  final num? matchIngAmt;
  final num? totalPnl;
  final String? productTypeName;
  final num? marketPrice;
  final num? ceilingPrice;
  final num? floorPrice;
  final num? refPrice;

  // kiểm tra xem danh mục này có phải được merge lại không.
  @JsonKey(includeToJson: false)
  final bool? isMergeSymbol;

  // kiểm tra một symbol có tồn tại trong 2 tiểu khoản
  @JsonKey(includeToJson: false)
  final bool? isSymbolInBothSubAccounts;

  /// giá vốn
  //  Nếu có cả mã WFT, giá vốn = (total mã gốc * costPrice mã gốc + total mã WFT * costPricemã WFT) / (total mã gốc + total mã WFT)
  // trường hợp khác . Giá vốn = Giá vốn hiển thị /1000 (làm tròn đến chữ số thập phân thứ 2)
  @JsonKey(includeToJson: false)
  final num? formatPrice;

  HoldingPortfolioStockModel({
    this.productTypeCd,
    this.custodyCd,
    this.accountId,
    this.symbol,
    this.secType,
    this.market,
    this.total,
    this.trade,
    this.blocked,
    this.vsdMortgage,
    this.mortgage,
    this.restrict,
    this.receivingRight,
    this.receivingT0,
    this.receivingT1,
    this.receivingT2,
    this.costPrice,
    this.costPriceAmt,
    this.basicPrice,
    this.basicPriceAmt,
    this.marginAmt,
    this.pnlAmt,
    this.pnlRate,
    this.isSell,
    this.withdraw,
    this.matchIngAmt,
    this.totalPnl,
    this.productTypeName,
    this.marketPrice,
    this.ceilingPrice,
    this.floorPrice,
    this.refPrice,
    this.formatPrice,
    this.isMergeSymbol,
    this.isSymbolInBothSubAccounts,
  });
  factory HoldingPortfolioStockModel.fromJson(Map<String, dynamic> json) =>
      _$HoldingPortfolioStockModelFromJson(json);

  Map<String, dynamic> toJson() => _$HoldingPortfolioStockModelToJson(this);

  HoldingPortfolioStockModel copyWith({
    String? custodyCd,
    String? accountId,
    String? symbol,
    String? secType,
    String? market,
    num? total,
    num? trade,
    num? blocked,
    num? vsdMortgage,
    num? mortgage,
    num? restrict,
    num? receivingRight,
    num? receivingT0,
    num? receivingT1,
    num? receivingT2,
    double? costPrice,
    double? costPriceAmt,
    num? basicPrice,
    num? basicPriceAmt,
    String? marginAmt,
    String? pnlAmt,
    String? pnlRate,
    String? isSell,
    num? withdraw,
    num? matchIngAmt,
    num? totalPnl,
    String? productTypeName,
    num? marketPrice,
    num? ceilingPrice,
    num? floorPrice,
    num? refPrice,
    num? formatPrice,
    bool? isMergeSymbol,
    String? productTypeCd,
    bool? isSymbolInBothSubAccounts,
  }) {
    return HoldingPortfolioStockModel(
      custodyCd: custodyCd ?? this.custodyCd,
      accountId: accountId ?? this.accountId,
      symbol: symbol ?? this.symbol,
      secType: secType ?? this.secType,
      market: market ?? this.market,
      total: total ?? this.total,
      trade: trade ?? this.trade,
      blocked: blocked ?? this.blocked,
      vsdMortgage: vsdMortgage ?? this.vsdMortgage,
      mortgage: mortgage ?? this.mortgage,
      restrict: restrict ?? this.restrict,
      receivingRight: receivingRight ?? this.receivingRight,
      receivingT0: receivingT0 ?? this.receivingT0,
      receivingT1: receivingT1 ?? this.receivingT1,
      receivingT2: receivingT2 ?? this.receivingT2,
      costPrice: costPrice ?? this.costPrice,
      costPriceAmt: costPriceAmt ?? this.costPriceAmt,
      basicPrice: basicPrice ?? this.basicPrice,
      basicPriceAmt: basicPriceAmt ?? this.basicPriceAmt,
      marginAmt: marginAmt ?? this.marginAmt,
      pnlAmt: pnlAmt ?? this.pnlAmt,
      pnlRate: pnlRate ?? this.pnlRate,
      isSell: isSell ?? this.isSell,
      withdraw: withdraw ?? this.withdraw,
      matchIngAmt: matchIngAmt ?? this.matchIngAmt,
      totalPnl: totalPnl ?? this.totalPnl,
      productTypeName: productTypeName ?? this.productTypeName,
      marketPrice: marketPrice ?? this.marketPrice,
      ceilingPrice: ceilingPrice ?? this.ceilingPrice,
      floorPrice: floorPrice ?? this.floorPrice,
      refPrice: refPrice ?? this.refPrice,
      formatPrice: formatPrice ?? this.formatPrice,
      isMergeSymbol: isMergeSymbol ?? this.isMergeSymbol,
      productTypeCd: productTypeCd ?? this.productTypeCd,
      isSymbolInBothSubAccounts:
          isSymbolInBothSubAccounts ?? this.isSymbolInBothSubAccounts,
    );
  }
}

extension HoldingPortfolioStockModelExtension on HoldingPortfolioStockModel {
  // custom mã ck
  // Chỉ hiển thị các mã chứng khoán thường, không hiển thị các mã chứng khoán có dạng XXX_WFT.
  // Nếu mã có dạng XXX_WFT thì hiển thị XXX
  String get customSymbol {
    if (symbol!.contains('_WFT')) {
      return symbol!.split('_')[0];
    }
    return symbol ?? '-';
  }

  //Gốc đầu tư = Giá vốn của cổ phiếu * Khối lượng cổ phiếu
  num get costPriceValue {
    return ((costPrice ?? 0) * (total ?? 0));
  }

  /// giá vốn
  //  Nếu có cả mã WFT, giá vốn = (total mã gốc * costPrice mã gốc + total mã WFT * costPricemã WFT) / (total mã gốc + total mã WFT)
  // trường hợp khác . Giá vốn = Giá vốn hiển thị /1000 (làm tròn đến chữ số thập phân thứ 2)
  String? get formatPriceView => FormatUtils.formatCurrency(
    (costPrice ?? 0) / 1000,
    convertToThousand: false,
    currency: '',
    trimZero: true,
  );

  Color? get colorPrice {
    if (marketPrice == null) return null;

    return StockColorUtils.colorByPrice(
      referencePrice: refPrice?.toDouble() ?? 0,
      currentPrice: marketPrice ?? 0,
      ceilingPrice: ceilingPrice?.toDouble(),
      floorPrice: floorPrice?.toDouble(),
    );
  }

  //Giá trị thị trường = =Giá thị trường * total
  // Thứ tự ưu tiên marketPrice> refPrice > basicPrice
  num get marketValue {
    return priceMarketPrice * (total ?? 0);
  }

  // Lãi/lỗ
  // Thứ tự ưu tiên marketPrice> basicPrice
  // Giá khớp hiển thị /1000 (làm tròn đến chữ số thập phân thứ 2).
  // Lãi/lỗ = (Giá trị thị trường-Giá trị vốn) / 1000
  // Giá trị thị trường	=Giá thị trường * total
  // Thứ tự ưu tiên marketPrice> refPrice > basicPrice
  num get pnlValue {
    final num quantity = total ?? 0;
    final num cost = costPriceAmt ?? 0;

    return (priceMarketPrice * quantity) - cost;
  }

  // thêm dấu + hoặc -
  String? get pnlView {
    return MoneyUtils.formatMoney(pnlValue.toDouble());
  }

  String get pnlRateDirectionSymbol {
    if (pnlRateView == null) return "";
    if (pnlValue > 0) {
      return "+";
    }
    return "";
  }

  //   Màu của lãi/lỗ dự kiến:
  // Nếu giá trị Lãi/lỗ dự kiến > 0 → hiển thị màu xanh lá cây
  // Nếu giá trị Lãi/lỗ dự kiến < 0 → hiển thị màu đỏ
  // Nếu giá trị Lãi/lỗ dự kiến = 0 →
  // hiển thị màu xám
  Color get colorPnl {
    if (pnlView == null) return vpColor.textAccentYellow;
    if (pnlValue > 0) {
      return vpColor.textPriceGreen;
    } else if (pnlValue < 0) {
      return vpColor.textPriceRed;
    } else {
      return vpColor.textAccentYellow;
    }
  }

  // % lãi/lỗ = Lãi/lỗ dự kiến / Gốc đầu tư * 100%

  String? get pnlRateView {
    if (pnlView == null) return "";
    if (costPriceAmt == null || costPriceAmt == 0) return "";
    return FormatUtils.formatPercent(
      (pnlValue / (costPriceAmt ?? 1)) * 100,
      showSign: false,
    );
  }

  // % thay đổi = (Giá khớp - Giá tham chiếu) / Giá tham chiếu * 100%
  // Thứ tự ưu tiên marketPrice> refPrice > basicPrice
  num get priceMarketPrice => marketPrice ?? refPrice ?? basicPrice ?? 0;

  bool get isBassicPrice => marketPrice == null && refPrice == null;

  num get todayChangePercent {
    final num? price = marketPrice ?? refPrice ?? basicPrice;
    if (refPrice == null || refPrice == 0 || price == null) return 0;
    return (price - refPrice!) / refPrice! * 100;
  }
}
