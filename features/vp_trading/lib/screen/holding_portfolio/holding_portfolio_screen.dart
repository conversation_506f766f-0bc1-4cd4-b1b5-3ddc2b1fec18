import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/cubit/holding_portfolio/list_holding_portfolio/list_holding_portfolio_cubit.dart';
import 'package:vp_trading/cubit/holding_portfolio/list_holding_portfolio/list_holding_portfolio_state.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/screen/holding_portfolio/widget/account_summary_widget.dart';
import 'package:vp_trading/screen/holding_portfolio/widget/content_holding_portfolio.dart';
import 'package:vp_trading/screen/holding_portfolio/widget/holding_portfolio_loading_widget.dart';
import 'package:vp_trading/screen/holding_portfolio/widget/no_data_holding.dart';
import 'package:vp_trading/utils/stock_utils.dart';

class HoldingPortfolioScreen extends StatefulWidget {
  const HoldingPortfolioScreen({super.key});

  @override
  State<HoldingPortfolioScreen> createState() => _HoldingPortfolioScreenState();
}

class _HoldingPortfolioScreenState extends State<HoldingPortfolioScreen> {
  final cubit = ListHoldingPortfolioCubit();

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => cubit..fetchHoldingPortfolio(),
      child: VPScaffold(
        appBar: VPAppBar.layer(
          title: VPTradingLocalize.current.trading_holding_portfolio,
          actions: [_buildSearchButton(context)],
        ),
        body: Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _filterSubAccount(context),
              const SizedBox(height: 24),
              Expanded(child: _buildListPortfolio()),
            ],
          ),
        ),
      ),
    );
  }

  Widget _filterSubAccount(BuildContext context) {
    return BlocBuilder<ListHoldingPortfolioCubit, ListHoldingPortfolioState>(
      builder: (context, state) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              VPTradingLocalize.current.trading_sub_account,
              style: context.textStyle.body14?.copyWith(
                color: vpColor.textPrimary,
              ),
            ),
            GestureDetector(
              onTap: () => _showSubAccountBottomSheet(context),
              child: Row(
                children: [
                  Text(
                    state.subAccount == null ||
                            state.subAccount?.accountType == SubAccountType.all
                        ? VPTradingLocalize.current.trading_all
                        : StockUtils.getSubAccountName(
                          state.subAccount?.id ?? "",
                        ),
                    style: context.textStyle.body14?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Icon(Icons.chevron_right, color: themeData.gray500),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildListPortfolio() {
    return BlocBuilder<ListHoldingPortfolioCubit, ListHoldingPortfolioState>(
      builder: (context, state) {
        if (state.apiStatus.isLoading) {
          return const HoldingPortfolioLoadingWidget();
        }
        if (state.apiStatus.isError) {
          return const ErrorView2();
        }
        if (state.apiStatus.isDone) {
          if (state.portfolioList.isEmpty) {
            return const NoDataHolding();
          }
        }
        return PullToRefreshView(
          onRefresh: () async {
            await cubit.fetchHoldingPortfolio();
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              children: [
                AccountSummaryWidget(
                  marketValue:
                      context
                          .read<ListHoldingPortfolioCubit>()
                          .marketValueTotal,
                  capitalValue:
                      context.read<ListHoldingPortfolioCubit>().capitalValue,
                  profit: context.read<ListHoldingPortfolioCubit>().profit,
                  profitPercent:
                      context.read<ListHoldingPortfolioCubit>().profitPercent,
                ),
                const SizedBox(height: 16),
                const DividerWidget(),
                const SizedBox(height: 16),
                ContentholdingPortfolio(portfolioList: state.portfolioList),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSearchButton(BuildContext context) {
    return GestureDetector(
      onTap: () => _showSearchOrderContainer(context),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: DesignAssets.icons.icSearch.svg(
          colorFilter: ColorFilter.mode(vpColor.iconPrimary, BlendMode.srcIn),
          height: 20,
        ),
      ),
    );
  }

  void _showSearchOrderContainer(BuildContext context) {
    context
        .pushNamed(
          VPStockCommonRouter.search.routeName,
          queryParameters:
              SearchArgs(
                itemAction: SearchItemAction.pickAndReturn,
              ).toQueryParams(),
        )
        .then((value) {
          if (value != null && value is StockModel) {
            cubit.updateSymbol(value.symbol);
          }
        });
  }

  Future<void> _showSubAccountBottomSheet(BuildContext context) async {
    var listSubAccounts = GetIt.instance<SubAccountCubit>().subAccountsStock;
    listSubAccounts.insert(0, subAccountTypeAll);
    final result = await showDerivativeSubAccountBottomSheet(
      context: context,
      listSubAccounts: listSubAccounts,
    );
    if (result is! SubAccountModel || !context.mounted) return;
    cubit.updateSubAccount(result);
  }
}
