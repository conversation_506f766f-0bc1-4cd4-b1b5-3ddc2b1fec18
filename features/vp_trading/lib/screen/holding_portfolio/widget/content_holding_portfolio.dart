import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:vp_common/utils/format_utils.dart';
import 'package:vp_common/utils/money_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/widgets/vp_close_price_item_view.dart';
import 'package:vp_trading/cubit/holding_portfolio/list_holding_portfolio/list_holding_portfolio_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/holding_portfolio/holding_portfolio_stock_model.dart';
import 'package:vp_trading/screen/holding_portfolio/widget/bottom_sheet_holding_portfolio.dart';

class ContentholdingPortfolio extends StatelessWidget {
  const ContentholdingPortfolio({super.key, this.portfolioList});
  final List<HoldingPortfolioStockModel>? portfolioList;

  @override
  Widget build(BuildContext context) {
    return DataTable2(
      smRatio: 0.67,
      lmRatio: 1.5,
      columnSpacing: 12,
      horizontalMargin: 0,
      dataRowHeight: 60,
      columns: _buildColumns(context),
      rows: portfolioList!.map((item) => _buildDataRow(context, item)).toList(),
    );
  }

  List<DataColumn2> _buildColumns(BuildContext context) {
    return [
      _buildColumn(
        VPTradingLocalize.current.trading_stock_code,
        context,
        ColumnSize.L,
        fixedWidth: 100,
        alignment: MainAxisAlignment.start,
      ),
      _buildColumn(
        VPTradingLocalize.current.trading_cost_price,
        context,
        ColumnSize.S,
        alignment: MainAxisAlignment.end,
      ),
      _buildColumn(
        VPTradingLocalize.current.trading_volume_title,
        context,
        ColumnSize.M,
        alignment: MainAxisAlignment.end,
      ),
      _buildColumn(
        VPTradingLocalize.current.trading_expected_profit_loss,
        context,
        ColumnSize.L,
        alignment: MainAxisAlignment.end,
      ),
    ];
  }

  DataColumn2 _buildColumn(
    String label,
    BuildContext context,
    ColumnSize size, {
    MainAxisAlignment alignment = MainAxisAlignment.start,
    double? fixedWidth,
  }) {
    return DataColumn2(
      headingRowAlignment: alignment,
      label: AutoSizeText(
        label,
        style: context.textStyle.captionRegular?.copyWith(
          color: vpColor.textDisabled,
        ),
        maxLines: 1,
        minFontSize: 10,
      ),
      size: size,
      fixedWidth: fixedWidth,
    );
  }

  DataRow2 _buildDataRow(
    BuildContext context,
    HoldingPortfolioStockModel item,
  ) {
    return DataRow2(
      onTap: () => _openDetailPortfolio(context, item),
      cells: [
        _buildSymbolCell(context, item),
        _buildPriceCell(context, item),
        _buildVolumeCell(context, item),
        _buildProfitLossCell(context, item),
      ],
    );
  }

  DataCell _buildSymbolCell(
    BuildContext context,
    HoldingPortfolioStockModel item,
  ) {
    return DataCell(
      Align(
        alignment: Alignment.centerLeft,
        child: PortfolioSymbolInfo(item: item),
      ),
    );
  }

  DataCell _buildPriceCell(
    BuildContext context,
    HoldingPortfolioStockModel item,
  ) {
    return DataCell(
      Align(
        alignment: Alignment.centerRight,
        child: Text(
          item.formatPriceView ?? "-",
          style: context.textStyle.subtitle14?.copyWith(
            color: vpColor.textPrimary,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  DataCell _buildVolumeCell(
    BuildContext context,
    HoldingPortfolioStockModel item,
  ) {
    return DataCell(
      Align(
        alignment: Alignment.centerRight,
        child: Text(
          MoneyUtils.formatMoney((item.total ?? 0).toDouble(), suffix: ''),
          style: context.textStyle.body14?.copyWith(color: vpColor.textPrimary),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  DataCell _buildProfitLossCell(
    BuildContext context,
    HoldingPortfolioStockModel item,
  ) {
    return DataCell(
      Align(
        alignment: Alignment.centerRight,
        child: PortfolioProfitLossInfo(item: item),
      ),
    );
  }

  void _openDetailPortfolio(
    BuildContext context,
    HoldingPortfolioStockModel item,
  ) {
    VPPopup.bottomSheet(
      BottomSheetHoldingPortfolio(
        item: item,
        subAccountFilter:
            context.read<ListHoldingPortfolioCubit>().state.subAccount,
        marketValueCategory:
            context.read<ListHoldingPortfolioCubit>().marketValueTotal,
      ),
    ).showSheet(context);
  }
}

class PortfolioSymbolInfo extends StatelessWidget {
  final HoldingPortfolioStockModel item;

  const PortfolioSymbolInfo({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          item.customSymbol,
          style: context.textStyle.subtitle14?.copyWith(
            color: vpColor.textPrimary,
          ),
        ),
        const SizedBox(height: 2),
        IgnorePointer(
          child: VPClosePriceItemView(
            textAlign: TextAlign.start,
            alignment: Alignment.centerLeft,
            symbol: item.symbol ?? "-",
            initClosePrice: item.priceMarketPrice,
            initTextColor:
                item.isBassicPrice ? vpColor.textPrimary : item.colorPrice,
            styleBuilder: (closePrice, color) {
              return vpTextStyle.captionMedium.copyColor(
                color ?? vpColor.textPrimary,
              );
            },
            priceBuilder: (closePrice) => _buildPriceText(closePrice),
          ),
        ),
      ],
    );
  }

  String _buildPriceText(num? closePrice) {
    if (closePrice == null) return '-.-';

    var price = FormatUtils.formatClosePrice(closePrice);
    var percentChange =
        item.refPrice != null && item.refPrice != 0
            ? ((closePrice - (item.refPrice ?? 0)) / (item.refPrice ?? 1) * 100)
            : 0.0;
    var percent = FormatUtils.formatPercent(percentChange, showSign: true);
    return "$price ($percent)";
  }
}

class PortfolioProfitLossInfo extends StatelessWidget {
  final HoldingPortfolioStockModel item;

  const PortfolioProfitLossInfo({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        AutoSizeText(
          "${item.pnlRateDirectionSymbol} ${item.pnlView ?? '-'}",
          style: context.textStyle.subtitle14?.copyWith(color: item.colorPnl),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          minFontSize: 10,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildPnlDirectionSymbol(),
            AutoSizeText(
              item.pnlRateView ?? '-',
              style: context.textStyle.body14?.copyWith(color: item.colorPnl),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              minFontSize: 10,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPnlDirectionSymbol() {
    if (item.pnlView == null) return const SizedBox();
    if (item.pnlValue > 0) {
      return Icon(Icons.arrow_drop_up, color: item.colorPnl);
    } else if (item.pnlValue < 0) {
      return Icon(Icons.arrow_drop_down, color: item.colorPnl);
    }
    return const SizedBox();
  }
}
