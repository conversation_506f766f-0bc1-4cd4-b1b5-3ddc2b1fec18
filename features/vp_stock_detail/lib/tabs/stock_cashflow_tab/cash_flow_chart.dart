import 'package:flutter/material.dart';
import 'package:stock_detail/model/axis_y_chart_widget.dart';
import 'package:stock_detail/model/chart_buy_up_sell_down_data.dart';
import 'package:stock_detail/model/chart_buy_up_sell_entity.dart';
import 'package:stock_detail/model/enum/buy_up_sell_down_type.dart';
import 'package:stock_detail/tabs/stock_cashflow_tab/stock_cash_flow_cubit.dart';
import 'package:stock_detail/tabs/stock_cashflow_tab/widgets/title_axis_y_chart_widget.dart';
import 'package:stock_detail/tabs/stock_cashflow_tab/widgets/tooltip_view.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/widget/divider/divider_view.dart';

class CashFlowChart extends StatefulWidget {
  const CashFlowChart({required this.type, required this.entity, super.key});

  final ChartBuyUpSellType type;

  final ChartBuyUpSellEntity entity;

  @override
  State<CashFlowChart> createState() => _CashFlowChartState();
}

class _CashFlowChartState extends State<CashFlowChart> {
  ChartBuyUpSellEntity get chartEntity => widget.entity;

  ChartBuyUpSellType get type => widget.type;

  num getMaxMin() {
    switch (type) {
      case ChartBuyUpSellType.small:
        return chartEntity.getMaxMinCharts([
          chartEntity.sChartDataBuy,
          chartEntity.sChartDataSell,
        ]);
      case ChartBuyUpSellType.medium:
        return chartEntity.getMaxMinCharts([
          chartEntity.mChartDataBuy,
          chartEntity.mChartDataSell,
        ]);
      case ChartBuyUpSellType.long:
        return chartEntity.getMaxMinCharts([
          chartEntity.lChartDataBuy,
          chartEntity.lChartDataSell,
        ]);
      default:
        return chartEntity.getMaxMinCharts([
          chartEntity.lChartDataBuy,
          chartEntity.lChartDataSell,
          chartEntity.mChartDataBuy,
          chartEntity.mChartDataSell,
          chartEntity.sChartDataBuy,
          chartEntity.sChartDataSell,
        ]);
    }
  }

  @override
  Widget build(BuildContext context) {
    double height = 280;

    double maxMinValue = getMaxMin().toDouble();
    double maxValue = maxMinValue.isZero() ? 1 : maxMinValue;
    double minValue = maxMinValue.isZero() ? 1 : (maxMinValue * -1);

    final obj = chartEntity;

    List<CartesianSeries> cartesianSeries = [];

    switch (type) {
      case ChartBuyUpSellType.small:
        cartesianSeries = [
          splineAreaSeriesBuy(obj.sChartDataBuy),
          splineAreaSeriesSell(obj.sChartDataSell),
        ];
        break;
      case ChartBuyUpSellType.medium:
        cartesianSeries = [
          splineAreaSeriesBuy(obj.mChartDataBuy),
          splineAreaSeriesSell(obj.mChartDataSell),
        ];
        break;
      case ChartBuyUpSellType.long:
        cartesianSeries = [
          splineAreaSeriesBuy(obj.lChartDataBuy),
          splineAreaSeriesSell(obj.lChartDataSell),
        ];
        break;
      default:
        cartesianSeries = [
          splineAreaSeriesBuy(obj.sChartDataBuy),
          splineAreaSeriesSell(obj.sChartDataSell),
          splineAreaSeriesBuy(obj.mChartDataBuy),
          splineAreaSeriesSell(obj.mChartDataSell),
          splineAreaSeriesBuy(obj.lChartDataBuy),
          splineAreaSeriesSell(obj.lChartDataSell),
        ];
        break;
    }

    return SizedBox(
      height: height,
      child: Row(
        children: [
          const TitleAxisYChartWidget(),
          const AxisYChartWidget(),
          Expanded(
            child: Stack(
              alignment: Alignment.centerLeft,
              clipBehavior: Clip.none,
              children: [
                Positioned(
                  left: -3,
                  right: 0,
                  child: const VPDividerView(height: 1),
                ),
                Positioned.fill(
                  left: -3,
                  child: SfCartesianChart(
                    margin: EdgeInsets.zero,
                    series: cartesianSeries,
                    trackballBehavior: TrackballBehavior(
                      enable: true,
                      lineDashArray: const [5, 5],
                      markerSettings: TrackballMarkerSettings(
                        markerVisibility: TrackballVisibilityMode.visible,
                        height: 10,
                        width: 10,
                        borderWidth: 5,
                        shape: DataMarkerType.circle,
                      ),
                      lineWidth: 0.5,
                      tooltipDisplayMode: TrackballDisplayMode.groupAllPoints,
                      activationMode: ActivationMode.singleTap,
                      tooltipAlignment: ChartAlignment.near,
                      builder: (context, trackballDetails) {
                        return StockCashFlowTooltipView(
                          type: context.read<StockCashFlowCubit>().state.type,
                          points: [
                            ...?trackballDetails.groupingModeInfo?.points,
                          ],
                        );
                      },
                    ),
                    primaryXAxis: const CategoryAxis(
                      labelPlacement: LabelPlacement.onTicks,
                      isVisible: false,
                    ),
                    primaryYAxis: NumericAxis(
                      isVisible: false,
                      minimum: minValue,
                      maximum: maxValue,
                      plotBands: <PlotBand>[
                        PlotBand(
                          isVisible: true,
                          start: 0,
                          end: 0,
                          borderWidth: 1,
                        ),
                      ],
                    ),
                    plotAreaBorderWidth: 0,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  SplineAreaSeries splineAreaSeriesBuy(List<ChartBuyUpSellData?> charts) {
    return SplineAreaSeries<ChartBuyUpSellData?, String>(
      splineType: SplineType.cardinal,
      dataSource: charts,
      xValueMapper: (ChartBuyUpSellData? data, _) => data?.x,
      yValueMapper: (ChartBuyUpSellData? data, _) => data?.y,
      color: vpColor.chartGreen.withValues(alpha: 0.16),
      borderColor: vpColor.strokeGreen,
      borderWidth: 1,
      animationDuration: 0,
    );
  }

  SplineAreaSeries splineAreaSeriesSell(List<ChartBuyUpSellData?> charts) {
    return SplineAreaSeries<ChartBuyUpSellData?, String>(
      splineType: SplineType.cardinal,
      dataSource: charts,
      xValueMapper: (ChartBuyUpSellData? data, _) => data?.x,
      yValueMapper: (ChartBuyUpSellData? data, _) => data?.y,
      color: vpColor.chartRed.withOpacity(0.16),
      borderColor: vpColor.strokeDanger,
      borderWidth: 1,
      animationDuration: 0,
    );
  }
}
